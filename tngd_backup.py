#!/usr/bin/env python3
"""
TNGD Unified Backup System
==========================

A unified Python-based backup solution that eliminates .bat file dependencies.
Supports CLI usage with flexible date input options for backing up Devo tables to OSS.

Features:
- No arguments: Back up today's data
- Single date: Back up specific date
- Date range: Back up data for each date in range
- Modular design with reusable functions
- Robust logging and exception handling
- Progress updates and cleanup

Usage:
    python tngd_backup.py                           # Today's data
    python tngd_backup.py 01 March 2025             # Single date
    python tngd_backup.py --start "01 March 2025" --end "31 March 2025"  # Date range

Author: TNGD Backup System
Version: 1.0.0
"""

import argparse
import json
import logging
import os
import shutil
import sys
import tempfile
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import existing core modules
try:
    from core.devo_client import DevoClient
    from core.storage_manager import StorageManager
    from core.config_manager import ConfigManager
    from utils.notification_service import NotificationService
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all required modules are available in the core/ and utils/ directories.")
    sys.exit(1)


class TngdBackupCLI:
    """
    Unified TNGD Backup CLI class that handles all backup operations.
    """
    
    def __init__(self):
        """Initialize the backup CLI with configuration and clients."""
        self.config_manager = ConfigManager()
        self.config = self.config_manager.config

        # Initialize basic logger immediately
        self.logger = logging.getLogger(__name__)

        self.devo_client = None
        self.storage_manager = None
        self.notification_service = None
        self.temp_files = []  # Track temporary files for cleanup
        
    def setup_logging(self, verbose: bool = False) -> None:
        """
        Setup logging configuration.

        Args:
            verbose: Enable verbose logging
        """
        log_level = logging.DEBUG if verbose else logging.INFO

        # Setup basic logging configuration
        log_dir = self.config_manager.get('logging', 'log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, 'tngd_backup.log')

        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)
        
    def initialize_clients(self) -> None:
        """Initialize Devo and Storage clients."""
        try:
            self.logger.info("Initializing Devo API client...")
            self.devo_client = DevoClient()
            
            self.logger.info("Initializing Storage Manager...")
            self.storage_manager = StorageManager(self.config_manager)

            self.logger.info("Initializing Notification Service...")
            self.notification_service = NotificationService(self.config_manager)

            self.logger.info("All clients initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize clients: {str(e)}")
            raise
            
    def load_table_list(self, config_path: Optional[str] = None) -> List[str]:
        """
        Load list of tables from configuration file.
        
        Args:
            config_path: Optional path to table configuration file
            
        Returns:
            List of table names
            
        Raises:
            FileNotFoundError: If configuration file not found
            json.JSONDecodeError: If configuration file is invalid JSON
        """
        # Determine configuration file path
        if config_path:
            table_config_path = Path(config_path)
        else:
            # Use paths from configuration
            monthly_config = self.config_manager.config.get('backup', {}).get('monthly_backup', {})
            config_section = monthly_config.get('configuration', {})
            config_paths = config_section.get('table_config_paths', [
                'tables.json',
                'config/tabletest/tables.json',
                'backup/tables.json'
            ])

            table_config_path = None
            for path in config_paths:
                if Path(path).exists():
                    table_config_path = Path(path)
                    break

            if not table_config_path:
                # Use fallback tables from configuration
                fallback_tables = config_section.get('fallback_table_list', [
                    "my.app.tngd.waf",
                    "my.app.tngd.actiontraillinux"
                ])
                self.logger.warning("Using fallback table list - no configuration file found")
                return fallback_tables
        
        if not table_config_path.exists():
            raise FileNotFoundError(f"Table configuration file not found: {table_config_path}")
            
        try:
            with open(table_config_path, 'r', encoding='utf-8') as f:
                tables = json.load(f)
                
            if not isinstance(tables, list):
                raise ValueError("Table configuration must be a JSON array")
                
            self.logger.info(f"Loaded {len(tables)} tables from {table_config_path}")
            return tables
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in table configuration file: {e}")

    def parse_date_string(self, date_str: str) -> datetime:
        """
        Parse date string in various formats.

        Args:
            date_str: Date string to parse

        Returns:
            Parsed datetime object

        Raises:
            ValueError: If date string cannot be parsed
        """
        # Common date formats to try
        date_formats = [
            "%d %B %Y",      # 01 March 2025
            "%d %b %Y",      # 01 Mar 2025
            "%Y-%m-%d",      # 2025-03-01
            "%m/%d/%Y",      # 03/01/2025
            "%d/%m/%Y",      # 01/03/2025
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        raise ValueError(f"Unable to parse date string: {date_str}")

    def generate_date_range(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        Generate list of dates between start and end dates (inclusive).

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            List of datetime objects for each date in range

        Raises:
            ValueError: If start_date is after end_date
        """
        if start_date > end_date:
            raise ValueError("Start date cannot be after end date")

        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)

        self.logger.info(f"Generated date range: {len(dates)} dates from {start_date.date()} to {end_date.date()}")
        return dates

    def backup_table(self, table_name: str, target_date: datetime) -> Dict[str, Any]:
        """
        Execute the full backup routine for a given table and date with enhanced retry and chunking.

        Args:
            table_name: Name of the table to backup
            target_date: Date to backup data for

        Returns:
            Dictionary containing backup results and metadata
        """
        backup_start_time = datetime.now()
        result = self._initialize_backup_result(table_name, target_date, backup_start_time)

        # Get retry configuration
        retry_config = self.config_manager.get('backup', 'retry_configuration', {})
        max_retries = retry_config.get('max_table_retries', 5)

        # Try backup with retry logic
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt}/{max_retries} for table {table_name} on {target_date.date()}")
                else:
                    self.logger.info(f"Starting backup for table {table_name} on {target_date.date()}")

                # Check if table should use chunked processing
                if self._should_use_chunked_processing(table_name):
                    return self._backup_table_chunked(table_name, target_date, result, backup_start_time)
                else:
                    return self._backup_table_standard(table_name, target_date, result, backup_start_time)

            except Exception as e:
                if attempt < max_retries:
                    # Calculate retry delay with exponential backoff
                    delay = self._calculate_retry_delay(attempt, retry_config)
                    self.logger.warning(f"Backup attempt {attempt + 1} failed for {table_name}: {str(e)}")
                    self.logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                    continue
                else:
                    # Final attempt failed
                    self.logger.error(f"All {max_retries + 1} backup attempts failed for {table_name}")
                    return self._handle_backup_error(result, e, table_name, target_date, None, backup_start_time)

        # This should never be reached, but just in case
        return self._handle_backup_error(result, Exception("Unexpected error in retry logic"),
                                       table_name, target_date, None, backup_start_time)

    def _initialize_backup_result(self, table_name: str, target_date: datetime, start_time: datetime) -> Dict[str, Any]:
        """Initialize the backup result dictionary."""
        return {
            'table_name': table_name,
            'target_date': target_date.date(),
            'status': 'started',
            'start_time': start_time,
            'rows_processed': 0,
            'file_size_mb': 0,
            'upload_path': None,
            'error': None
        }

    def _query_table_data(self, table_name: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Query table data from Devo API with enhanced timeout and retry logic."""
        self.logger.info(f"Querying Devo API for {table_name}...")

        # Build query for the specific date
        date_str = target_date.strftime("%Y-%m-%d")
        next_date_str = (target_date + timedelta(days=1)).strftime('%Y-%m-%d')
        query = f"from {table_name} where eventdate >= '{date_str}' and eventdate < '{next_date_str}'"

        # Get dynamic timeout based on table size expectations
        timeout = self._get_dynamic_timeout(table_name)

        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        # Use retry logic for better reliability
        retry_config = self.config_manager.get('backup', 'retry_configuration', {})
        max_retries = retry_config.get('connection_retry_attempts', 3)

        try:
            query_results = self.devo_client.execute_query_with_retry(
                query=query,
                timeout=timeout,
                table_name=table_name,
                max_retries=max_retries
            )
        except AttributeError:
            # Fallback to standard method if retry method not available
            query_results = self.devo_client.execute_query(
                query=query,
                timeout=timeout,
                table_name=table_name
            )

        self.logger.info(f"Retrieved {len(query_results)} rows for {table_name}")
        return query_results

    def _get_dynamic_timeout(self, table_name: str) -> int:
        """
        Get dynamic timeout based on table name and configuration.

        Args:
            table_name: Name of the table

        Returns:
            Timeout in seconds
        """
        query_timeouts = self.config_manager.get('backup', 'query_timeouts', {})

        # Known large tables that need longer timeouts
        large_tables = {
            'cef0.zscaler.nssweblog': query_timeouts.get('ultra_large_table', 43200),
            'cloud.alibaba.log_service.events': query_timeouts.get('very_large_table', 21600),
            'cloud.office365.management.exchange': query_timeouts.get('large_table', 7200)
        }

        if table_name in large_tables:
            return large_tables[table_name]

        # Default timeout for other tables
        return self.config_manager.get('backup', 'default_timeout', 21600)

    def _handle_no_data(self, result: Dict[str, Any], table_name: str, target_date: datetime) -> Dict[str, Any]:
        """Handle case when no data is found."""
        self.logger.warning(f"No data found for {table_name} on {target_date.date()}")
        result['status'] = 'no_data'
        result['end_time'] = datetime.now()
        result['duration_seconds'] = (result['end_time'] - result['start_time']).total_seconds()
        return result

    def _save_data_to_temp_file(self, query_results: List[Dict[str, Any]], table_name: str,
                               target_date: datetime, result: Dict[str, Any]) -> str:
        """Save query results to temporary file."""
        temp_file = self._create_temp_file(table_name, target_date)
        self.temp_files.append(temp_file)

        self.logger.info(f"Saving data to temporary file: {temp_file}")
        self._save_query_results(query_results, temp_file)

        # Update result with file size
        file_size = os.path.getsize(temp_file)
        result['file_size_mb'] = file_size / (1024 * 1024)

        return temp_file

    def _upload_data_to_oss(self, temp_file: str, table_name: str, target_date: datetime,
                           result: Dict[str, Any]) -> None:
        """Upload data file to OSS."""
        upload_path = self._generate_oss_path(table_name, target_date)
        result['upload_path'] = upload_path

        self.logger.info(f"Uploading {table_name}_{target_date.date().strftime('%Y-%m-%d')}.json.gz to OSS...")
        upload_success, upload_details = self.upload_to_oss(temp_file, upload_path)

        if not upload_success:
            raise RuntimeError(f"Upload failed: {upload_details}")

    def _finalize_successful_backup(self, result: Dict[str, Any], table_name: str, target_date: datetime,
                                   temp_file: str, backup_start_time: datetime) -> Dict[str, Any]:
        """Finalize a successful backup operation."""
        # Cleanup temporary file
        self.cleanup_temp(temp_file)

        # Update result
        result['status'] = 'completed'
        result['end_time'] = datetime.now()
        result['duration_seconds'] = (result['end_time'] - backup_start_time).total_seconds()

        self.logger.info(f"Successfully backed up {table_name} for {target_date.date()}")
        return result

    def _handle_backup_error(self, result: Dict[str, Any], error: Exception, table_name: str,
                            target_date: datetime, temp_file: Optional[str],
                            backup_start_time: datetime) -> Dict[str, Any]:
        """Handle backup errors and cleanup."""
        result['status'] = 'failed'
        result['error'] = str(error)
        result['end_time'] = datetime.now()
        result['duration_seconds'] = (result['end_time'] - backup_start_time).total_seconds()

        self.logger.error(f"Backup failed for {table_name} on {target_date.date()}: {str(error)}")

        # Cleanup on failure - only if temp_file was created
        if temp_file and temp_file in self.temp_files:
            self.cleanup_temp(temp_file)

        return result

    def _create_temp_file(self, table_name: str, target_date: datetime) -> str:
        """
        Create a temporary file for storing query results.

        Args:
            table_name: Name of the table
            target_date: Target date for backup

        Returns:
            Path to temporary file
        """
        temp_dir = self.config_manager.get('storage', 'temp_dir', 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        date_str = target_date.strftime('%Y-%m-%d')
        temp_filename = f"{table_name}_{date_str}.json"
        temp_path = os.path.join(temp_dir, temp_filename)

        return temp_path

    def _save_query_results(self, results: List[Dict[str, Any]], file_path: str) -> None:
        """
        Save query results to a JSON file.

        Args:
            results: Query results to save
            file_path: Path to save file
        """
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

    def _generate_oss_path(self, table_name: str, target_date: datetime) -> str:
        """
        Generate OSS storage path for the backup file.

        Args:
            table_name: Name of the table
            target_date: Target date for backup

        Returns:
            OSS storage path
        """
        # Get path template from configuration
        path_template = self.config.get('storage', {}).get('oss_path_template',
                                                          'Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz')

        # Calculate date components
        month_name_str = target_date.strftime('%B')  # Full month name
        week_number = (target_date.day - 1) // 7 + 1  # Week number in month
        date_str = target_date.strftime('%Y-%m-%d')

        # Format the path
        oss_path = path_template.format(
            month_name_str=month_name_str,
            week_number=week_number,
            date_str=date_str,
            table_name=table_name
        )

        return oss_path

    def upload_to_oss(self, local_file: str, oss_path: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Upload file to OSS storage.

        Args:
            local_file: Path to local file to upload
            oss_path: OSS destination path

        Returns:
            Tuple of (success_flag, upload_details)
        """
        try:
            # Create a temporary directory with the file for compression
            temp_upload_dir = tempfile.mkdtemp(prefix='tngd_upload_')
            temp_file_in_dir = os.path.join(temp_upload_dir, os.path.basename(local_file))

            # Copy the file to the temporary directory
            shutil.copy2(local_file, temp_file_in_dir)

            try:
                # Use the storage manager's compress_and_upload method
                if self.storage_manager:
                    success, operation_details = self.storage_manager.compress_and_upload(
                        temp_upload_dir, oss_path, verify_integrity=True
                    )

                    if success:
                        return True, {
                            'oss_path': oss_path,
                            'operation_details': operation_details
                        }
                    else:
                        return False, {'error': 'Compression or upload failed'}
                else:
                    return False, {'error': 'Storage manager not initialized'}

            finally:
                # Clean up temporary directory
                shutil.rmtree(temp_upload_dir, ignore_errors=True)

        except Exception as e:
            self.logger.error(f"Upload failed: {str(e)}")
            return False, {'error': str(e)}

    def cleanup_temp(self, file_path: str) -> None:
        """
        Safely remove temporary files post-upload.

        Args:
            file_path: Path to temporary file to remove
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.debug(f"Cleaned up temporary file: {file_path}")

                # Remove from tracking list
                if file_path in self.temp_files:
                    self.temp_files.remove(file_path)

        except Exception as e:
            self.logger.warning(f"Failed to cleanup temporary file {file_path}: {str(e)}")

    def cleanup_all_temp_files(self) -> None:
        """Clean up all tracked temporary files."""
        for temp_file in self.temp_files.copy():
            self.cleanup_temp(temp_file)

    def _should_use_chunked_processing(self, table_name: str) -> bool:
        """
        Determine if a table should use chunked processing based on configuration and known large tables.

        Args:
            table_name: Name of the table to check

        Returns:
            True if chunked processing should be used
        """
        chunking_config = self.config_manager.get('backup', 'chunking_configuration', {})

        if not chunking_config.get('enable_chunking', True):
            return False

        # Known large tables that should always use chunking
        large_tables = [
            'cef0.zscaler.nssweblog',
            'cloud.alibaba.log_service.events',
            'cloud.office365.management.exchange'
        ]

        if table_name in large_tables:
            return True

        # Auto-detect based on configuration
        if chunking_config.get('auto_chunk_large_tables', True):
            # For now, we'll use chunking for tables that historically have issues
            # In the future, this could be enhanced with table size pre-checks
            return table_name in large_tables

        return False

    def _calculate_retry_delay(self, attempt: int, retry_config: Dict[str, Any]) -> float:
        """
        Calculate retry delay with exponential backoff.

        Args:
            attempt: Current attempt number (0-based)
            retry_config: Retry configuration dictionary

        Returns:
            Delay in seconds
        """
        base_delay = retry_config.get('retry_delay_base', 30)
        max_delay = retry_config.get('retry_delay_max', 300)
        use_exponential = retry_config.get('exponential_backoff', True)
        multiplier = retry_config.get('backoff_multiplier', 2)

        if use_exponential:
            delay = base_delay * (multiplier ** attempt)
        else:
            delay = base_delay * (attempt + 1)

        return min(delay, max_delay)

    def _backup_table_standard(self, table_name: str, target_date: datetime,
                              result: Dict[str, Any], backup_start_time: datetime) -> Dict[str, Any]:
        """
        Execute standard (non-chunked) backup for a table with enhanced logging.

        Args:
            table_name: Name of the table to backup
            target_date: Date to backup data for
            result: Initialized result dictionary
            backup_start_time: Backup start time

        Returns:
            Dictionary containing backup results and metadata
        """
        temp_file = None

        try:
            self.logger.info(f"📊 STANDARD PROCESSING for {table_name}")

            # Step 1: Query and validate data
            self.logger.info(f"📡 Step 1: Querying data from Devo API...")
            query_start_time = datetime.now()
            query_results = self._query_table_data(table_name, target_date)
            query_duration = (datetime.now() - query_start_time).total_seconds()

            result['rows_processed'] = len(query_results)
            self.logger.info(f"✅ Query completed: {result['rows_processed']:,} rows in {query_duration:.1f}s")

            if result['rows_processed'] == 0:
                self.logger.info(f"📭 No data found for {table_name}")
                return self._handle_no_data(result, table_name, target_date)

            # Step 2: Save data to temporary file
            self.logger.info(f"💾 Step 2: Saving data to temporary file...")
            save_start_time = datetime.now()
            temp_file = self._save_data_to_temp_file(query_results, table_name, target_date, result)
            save_duration = (datetime.now() - save_start_time).total_seconds()
            self.logger.info(f"✅ File saved in {save_duration:.1f}s")

            # Step 3: Upload to OSS
            self.logger.info(f"☁️  Step 3: Uploading to OSS...")
            upload_start_time = datetime.now()
            self._upload_data_to_oss(temp_file, table_name, target_date, result)
            upload_duration = (datetime.now() - upload_start_time).total_seconds()
            self.logger.info(f"✅ Upload completed in {upload_duration:.1f}s")

            # Step 4: Finalize successful backup
            total_duration = (datetime.now() - backup_start_time).total_seconds()
            self.logger.info(f"🎉 STANDARD BACKUP COMPLETED for {table_name} in {total_duration:.1f}s")
            return self._finalize_successful_backup(result, table_name, target_date, temp_file, backup_start_time)

        except Exception as e:
            self.logger.error(f"❌ STANDARD BACKUP FAILED for {table_name}: {str(e)}")
            return self._handle_backup_error(result, e, table_name, target_date, temp_file, backup_start_time)

    def _backup_table_chunked(self, table_name: str, target_date: datetime,
                             result: Dict[str, Any], backup_start_time: datetime) -> Dict[str, Any]:
        """
        Execute chunked backup for a large table by breaking it into time-based chunks.

        Args:
            table_name: Name of the table to backup
            target_date: Date to backup data for
            result: Initialized result dictionary
            backup_start_time: Backup start time

        Returns:
            Dictionary containing backup results and metadata
        """
        chunking_config = self.config_manager.get('backup', 'chunking_configuration', {})
        hours_per_chunk = chunking_config.get('hours_per_chunk', 6)
        overlap_minutes = chunking_config.get('chunk_overlap_minutes', 5)

        # Generate time chunks for the day
        chunks = self._generate_time_chunks(target_date, hours_per_chunk, overlap_minutes)

        # Enhanced logging for chunked processing
        self.logger.info(f"🧩 CHUNKED PROCESSING STARTED for {table_name}")
        self.logger.info(f"📊 Chunk Configuration: {hours_per_chunk}-hour chunks with {overlap_minutes}-minute overlap")
        self.logger.info(f"📅 Date: {target_date.date()}")
        self.logger.info(f"🔢 Total chunks to process: {len(chunks)}")

        # Log all chunk time ranges
        for i, (chunk_start, chunk_end) in enumerate(chunks, 1):
            self.logger.info(f"   Chunk {i}: {chunk_start.strftime('%H:%M:%S')} - {chunk_end.strftime('%H:%M:%S')}")

        all_results = []
        total_rows = 0
        chunk_files = []
        chunk_start_time = datetime.now()

        try:
            for i, (chunk_start, chunk_end) in enumerate(chunks, 1):
                chunk_process_start = datetime.now()

                # Log chunk start with enhanced formatting
                self._log_processing_status(table_name, "CHUNK", "STARTED",
                                          f"Chunk {i}/{len(chunks)} [{chunk_start.strftime('%H:%M')}-{chunk_end.strftime('%H:%M')}]")

                # Query chunk data with detailed logging
                chunk_results = self._query_table_data_chunk(table_name, chunk_start, chunk_end)

                chunk_process_time = (datetime.now() - chunk_process_start).total_seconds()

                if chunk_results:
                    # Save chunk to temporary file
                    chunk_file = self._save_chunk_to_temp_file(chunk_results, table_name, target_date, i)
                    chunk_files.append(chunk_file)
                    all_results.extend(chunk_results)
                    total_rows += len(chunk_results)

                    # Log chunk completion with detailed info
                    self._log_chunk_details(table_name, i, len(chunks), chunk_start, chunk_end,
                                          len(chunk_results), chunk_process_time)
                else:
                    self._log_processing_status(table_name, "CHUNK", "COMPLETED",
                                              f"Chunk {i}/{len(chunks)} - No data", chunk_process_time)

                # Progress summary every few chunks or for the last chunk
                if i % 2 == 0 or i == len(chunks):
                    remaining_chunks = len(chunks) - i
                    if remaining_chunks > 0:
                        avg_time_per_chunk = (datetime.now() - chunk_start_time).total_seconds() / i
                        estimated_remaining_time = remaining_chunks * avg_time_per_chunk
                        self._show_chunk_progress_summary(table_name, i, len(chunks),
                                                        total_rows, estimated_remaining_time/60)

            # Final chunk processing summary
            total_chunk_time = (datetime.now() - chunk_start_time).total_seconds()
            self.logger.info(f"🎯 CHUNKED PROCESSING COMPLETED for {table_name}")
            self.logger.info(f"📊 Summary: {len(chunks)} chunks processed in {total_chunk_time/60:.1f} minutes")
            self.logger.info(f"📈 Total rows collected: {total_rows:,}")

            if total_rows == 0:
                self.logger.info(f"📭 No data found across all chunks for {table_name}")
                return self._handle_no_data(result, table_name, target_date)

            # Combine all chunks into a single file
            self.logger.info(f"🔗 Combining {len(chunk_files)} chunk files into single file...")
            print(f"    🔗 Combining {len(chunk_files)} chunks... ", end="", flush=True)
            combined_file = self._combine_chunk_files(chunk_files, table_name, target_date)
            print(f"✅ Combined")

            # Update result
            result['rows_processed'] = total_rows
            file_size = os.path.getsize(combined_file)
            result['file_size_mb'] = file_size / (1024 * 1024)

            self.logger.info(f"📁 Combined file size: {result['file_size_mb']:.1f} MB")

            # Upload combined file to OSS
            self.logger.info(f"☁️  Uploading combined file to OSS...")
            self._upload_data_to_oss(combined_file, table_name, target_date, result)

            # Cleanup chunk files
            self.logger.info(f"🧹 Cleaning up {len(chunk_files)} temporary chunk files...")
            for chunk_file in chunk_files:
                self.cleanup_temp(chunk_file)

            # Finalize successful backup
            total_time = (datetime.now() - backup_start_time).total_seconds()
            self.logger.info(f"🎉 CHUNKED BACKUP COMPLETED for {table_name} in {total_time/60:.1f} minutes")
            return self._finalize_successful_backup(result, table_name, target_date, combined_file, backup_start_time)

        except Exception as e:
            self.logger.error(f"❌ CHUNKED PROCESSING FAILED for {table_name}: {str(e)}")
            # Cleanup chunk files on error
            for chunk_file in chunk_files:
                self.cleanup_temp(chunk_file)
            return self._handle_backup_error(result, e, table_name, target_date, None, backup_start_time)

    def _generate_time_chunks(self, target_date: datetime, hours_per_chunk: int,
                             overlap_minutes: int) -> List[Tuple[datetime, datetime]]:
        """
        Generate time-based chunks for a given date.

        Args:
            target_date: The date to chunk
            hours_per_chunk: Hours per chunk
            overlap_minutes: Minutes of overlap between chunks

        Returns:
            List of (start_time, end_time) tuples
        """
        chunks = []
        current_start = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        day_end = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        while current_start < day_end:
            chunk_end = current_start + timedelta(hours=hours_per_chunk)
            if chunk_end > day_end:
                chunk_end = day_end

            chunks.append((current_start, chunk_end))

            # Move to next chunk with overlap
            current_start = chunk_end - timedelta(minutes=overlap_minutes)

            # Prevent infinite loop
            if current_start >= chunk_end:
                break

        return chunks

    def _query_table_data_chunk(self, table_name: str, start_time: datetime,
                               end_time: datetime) -> List[Dict[str, Any]]:
        """
        Query table data for a specific time chunk with enhanced logging and retry logic.

        Args:
            table_name: Name of the table to query
            start_time: Chunk start time
            end_time: Chunk end time

        Returns:
            List of query results
        """
        # Build query for the specific time range
        start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
        end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
        query = f"from {table_name} where eventdate >= '{start_str}' and eventdate <= '{end_str}'"

        # Use dynamic timeout for chunks (usually shorter than full day)
        timeout = self._get_dynamic_timeout(table_name) // 4  # Quarter of full day timeout for chunks
        timeout = max(timeout, 1800)  # Minimum 30 minutes

        self.logger.info(f"📡 Executing chunk query with {timeout/60:.0f}-minute timeout")
        self.logger.debug(f"🔍 Query: {query}")

        if not self.devo_client:
            raise RuntimeError("Devo client not initialized")

        # Use retry logic for chunks as well
        retry_config = self.config_manager.get('backup', 'retry_configuration', {})
        max_retries = retry_config.get('connection_retry_attempts', 3)

        query_start_time = datetime.now()

        try:
            self.logger.info(f"🚀 Starting chunk query execution...")
            query_results = self.devo_client.execute_query_with_retry(
                query=query,
                timeout=timeout,
                table_name=table_name,
                max_retries=max_retries
            )
        except AttributeError:
            # Fallback to standard method if retry method not available
            self.logger.info(f"🔄 Using standard query method (retry method not available)")
            query_results = self.devo_client.execute_query(
                query=query,
                timeout=timeout,
                table_name=table_name
            )

        query_duration = (datetime.now() - query_start_time).total_seconds()

        if query_results:
            self.logger.info(f"✅ Chunk query completed: {len(query_results):,} rows in {query_duration:.1f}s")
            if len(query_results) > 100000:  # Log for large chunks
                self.logger.info(f"📊 Large chunk detected: {len(query_results):,} rows (processing rate: {len(query_results)/query_duration:.0f} rows/sec)")
        else:
            self.logger.info(f"📭 Chunk query completed: No data found in {query_duration:.1f}s")

        return query_results

    def _save_chunk_to_temp_file(self, chunk_results: List[Dict[str, Any]], table_name: str,
                                target_date: datetime, chunk_number: int) -> str:
        """
        Save chunk results to a temporary file.

        Args:
            chunk_results: Query results for the chunk
            table_name: Name of the table
            target_date: Target date
            chunk_number: Chunk number

        Returns:
            Path to temporary chunk file
        """
        temp_dir = self.config_manager.get('storage', 'temp_dir', 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        date_str = target_date.strftime('%Y-%m-%d')
        chunk_filename = f"{table_name}_{date_str}_chunk_{chunk_number}.json"
        chunk_path = os.path.join(temp_dir, chunk_filename)

        with open(chunk_path, 'w', encoding='utf-8') as f:
            json.dump(chunk_results, f, ensure_ascii=False, indent=2)

        self.temp_files.append(chunk_path)
        return chunk_path

    def _combine_chunk_files(self, chunk_files: List[str], table_name: str,
                           target_date: datetime) -> str:
        """
        Combine multiple chunk files into a single file with detailed progress logging.

        Args:
            chunk_files: List of chunk file paths
            table_name: Name of the table
            target_date: Target date

        Returns:
            Path to combined file
        """
        combine_start_time = datetime.now()
        combined_file = self._create_temp_file(table_name, target_date)
        combined_results = []

        self.logger.info(f"🔗 Starting chunk combination process for {table_name}")
        self.logger.info(f"📁 Combining {len(chunk_files)} chunk files...")

        # Read all chunk files and combine results
        for i, chunk_file in enumerate(chunk_files, 1):
            self.logger.info(f"📖 Reading chunk file {i}/{len(chunk_files)}: {os.path.basename(chunk_file)}")

            try:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunk_data = json.load(f)
                    combined_results.extend(chunk_data)
                    self.logger.info(f"✅ Chunk {i}: {len(chunk_data):,} records loaded")
            except Exception as e:
                self.logger.error(f"❌ Failed to read chunk file {i}: {str(e)}")
                raise

        total_records_before_dedup = len(combined_results)
        self.logger.info(f"📊 Total records before deduplication: {total_records_before_dedup:,}")

        # Remove duplicates based on overlap (if any)
        self.logger.info(f"🔍 Starting deduplication process...")
        dedup_start_time = datetime.now()

        seen_records = set()
        unique_results = []
        duplicates_found = 0

        for i, record in enumerate(combined_results):
            if i % 50000 == 0 and i > 0:  # Progress update every 50k records
                self.logger.info(f"🔄 Deduplication progress: {i:,}/{total_records_before_dedup:,} records processed")

            # Create a simple hash of the record for deduplication
            try:
                record_hash = hash(json.dumps(record, sort_keys=True))
                if record_hash not in seen_records:
                    seen_records.add(record_hash)
                    unique_results.append(record)
                else:
                    duplicates_found += 1
            except Exception as e:
                # If hashing fails, include the record anyway
                self.logger.warning(f"⚠️  Could not hash record {i}, including anyway: {str(e)}")
                unique_results.append(record)

        dedup_duration = (datetime.now() - dedup_start_time).total_seconds()
        self.logger.info(f"✅ Deduplication completed in {dedup_duration:.1f}s")
        self.logger.info(f"📊 Duplicates removed: {duplicates_found:,}")
        self.logger.info(f"📈 Final unique records: {len(unique_results):,}")

        # Save combined results
        self.logger.info(f"💾 Writing combined file...")
        write_start_time = datetime.now()

        with open(combined_file, 'w', encoding='utf-8') as f:
            json.dump(unique_results, f, ensure_ascii=False, indent=2)

        write_duration = (datetime.now() - write_start_time).total_seconds()
        file_size_mb = os.path.getsize(combined_file) / (1024 * 1024)

        self.temp_files.append(combined_file)

        total_combine_duration = (datetime.now() - combine_start_time).total_seconds()

        self.logger.info(f"✅ File write completed in {write_duration:.1f}s")
        self.logger.info(f"📁 Combined file size: {file_size_mb:.1f} MB")
        self.logger.info(f"🎯 CHUNK COMBINATION COMPLETED in {total_combine_duration:.1f}s")
        self.logger.info(f"📊 Summary: {len(chunk_files)} chunks → {len(unique_results):,} unique records")

        return combined_file

    def _log_processing_status(self, table_name: str, processing_type: str, status: str,
                              details: str = "", duration: Optional[float] = None) -> None:
        """
        Log processing status with consistent formatting for easy monitoring.

        Args:
            table_name: Name of the table being processed
            processing_type: Type of processing (CHUNKED, STANDARD, QUERY, etc.)
            status: Status (STARTED, IN_PROGRESS, COMPLETED, FAILED)
            details: Additional details
            duration: Duration in seconds if applicable
        """
        timestamp = datetime.now().strftime("%H:%M:%S")

        status_icons = {
            'STARTED': '🚀',
            'IN_PROGRESS': '🔄',
            'COMPLETED': '✅',
            'FAILED': '❌',
            'WARNING': '⚠️'
        }

        icon = status_icons.get(status, '📊')

        if duration is not None:
            duration_str = f" ({duration:.1f}s)"
        else:
            duration_str = ""

        log_message = f"{icon} [{timestamp}] {processing_type} {status} - {table_name}{duration_str}"
        if details:
            log_message += f" - {details}"

        self.logger.info(log_message)

        # Also print to console for immediate visibility
        if status in ['STARTED', 'COMPLETED', 'FAILED']:
            print(f"    {icon} {processing_type} {status}: {table_name}{duration_str}")
            if details and status == 'FAILED':
                print(f"        ⚠️  {details}")

    def _show_chunk_progress_summary(self, table_name: str, current_chunk: int,
                                   total_chunks: int, rows_so_far: int,
                                   estimated_remaining_minutes: float) -> None:
        """
        Show a progress summary for chunked processing.

        Args:
            table_name: Name of the table
            current_chunk: Current chunk number
            total_chunks: Total number of chunks
            rows_so_far: Total rows processed so far
            estimated_remaining_minutes: Estimated time remaining in minutes
        """
        progress_percent = (current_chunk / total_chunks) * 100

        progress_message = (
            f"📈 {table_name} Progress: {current_chunk}/{total_chunks} chunks "
            f"({progress_percent:.1f}%) - {rows_so_far:,} rows - "
            f"~{estimated_remaining_minutes:.1f}min remaining"
        )

        self.logger.info(progress_message)
        print(f"    {progress_message}")

    def _log_chunk_details(self, table_name: str, chunk_num: int, total_chunks: int,
                          start_time: datetime, end_time: datetime,
                          rows: int, duration: float) -> None:
        """
        Log detailed information about a completed chunk.

        Args:
            table_name: Name of the table
            chunk_num: Chunk number
            total_chunks: Total number of chunks
            start_time: Chunk start time
            end_time: Chunk end time
            rows: Number of rows in chunk
            duration: Processing duration in seconds
        """
        time_range = f"{start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')}"
        rate = f"{rows/duration:.0f} rows/s" if duration > 0 and rows > 0 else "N/A"

        chunk_message = (
            f"✅ Chunk {chunk_num}/{total_chunks} [{time_range}]: "
            f"{rows:,} rows in {duration:.1f}s ({rate})"
        )

        self.logger.info(f"[{table_name}] {chunk_message}")

        # Show progress bar for chunks
        progress_bar_length = 20
        filled_length = int(progress_bar_length * chunk_num // total_chunks)
        bar = '█' * filled_length + '░' * (progress_bar_length - filled_length)

        print(f"    📊 [{bar}] {chunk_num}/{total_chunks} - {rows:,} rows ({duration:.1f}s)")


def parse_args() -> argparse.Namespace:
    """
    Parse and validate CLI arguments.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description="TNGD Unified Backup System - Backup Devo tables to OSS",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Back up today's data
  %(prog)s 01 March 2025                     # Back up specific date
  %(prog)s --start "01 March 2025" --end "31 March 2025"  # Back up date range
  %(prog)s --dry-run                         # Validate configuration only
  %(prog)s --tables my.app.tngd.waf          # Back up specific table only
        """
    )
    
    # Single date (positional arguments)
    parser.add_argument(
        'date_parts',
        nargs='*',
        help='Date parts for single date backup (e.g., "01 March 2025")'
    )

    # Date range arguments (both required together)
    parser.add_argument(
        '--start',
        type=str,
        help='Start date for range backup (e.g., "01 March 2025")'
    )

    parser.add_argument(
        '--end',
        type=str,
        help='End date for range backup (e.g., "31 March 2025")'
    )
    
    # Configuration options
    parser.add_argument(
        '--config', 
        type=str, 
        help='Path to table configuration file (default: auto-detect)'
    )
    
    parser.add_argument(
        '--tables', 
        nargs='+', 
        help='Specific table names to backup (overrides config file)'
    )
    
    # Operation modes
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='Validate configuration and connectivity only, no backup'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Enable verbose logging'
    )
    

    
    args = parser.parse_args()

    # Validate that start and end are used together
    if (args.start and not args.end) or (args.end and not args.start):
        parser.error("--start and --end must be used together for date range backup")

    # Validate that date_parts and date range are not used together
    if args.date_parts and (args.start or args.end):
        parser.error("Cannot use both positional date arguments and --start/--end range arguments")

    return args


def setup_and_validate(args: argparse.Namespace) -> Tuple[TngdBackupCLI, List[str], List[datetime]]:
    """
    Setup and validate the backup environment.

    Args:
        args: Parsed command line arguments

    Returns:
        Tuple of (backup_cli, tables, dates)
    """
    # Initialize backup CLI
    backup_cli = TngdBackupCLI()
    backup_cli.setup_logging(verbose=args.verbose)

    backup_cli.logger.info("=== TNGD Unified Backup System Started ===")
    backup_cli.logger.info(f"Arguments: {vars(args)}")

    # Handle dry run mode
    if args.dry_run:
        return handle_dry_run(args, backup_cli)

    # Initialize clients for actual backup
    backup_cli.initialize_clients()

    # Load table configuration
    tables = load_table_configuration(args, backup_cli)
    backup_cli.logger.info(f"Backup will process {len(tables)} tables")

    # Determine target dates
    dates = determine_target_dates(args, backup_cli)
    backup_cli.logger.info(f"Processing {len(dates)} dates")

    return backup_cli, tables, dates


def handle_dry_run(args: argparse.Namespace, backup_cli: TngdBackupCLI) -> Tuple[TngdBackupCLI, List[str], List[datetime]]:
    """
    Handle dry run mode validation.

    Args:
        args: Parsed command line arguments
        backup_cli: Initialized backup CLI instance

    Returns:
        Tuple of (backup_cli, empty_tables, empty_dates) - function exits after validation
    """
    backup_cli.logger.info("DRY RUN MODE: Validating configuration only")

    # Test configuration loading
    tables = load_table_configuration(args, backup_cli)
    backup_cli.logger.info(f"Loaded {len(tables)} tables from configuration")

    # Test client initialization
    backup_cli.initialize_clients()
    backup_cli.logger.info("All clients initialized successfully")

    # Test date parsing
    dates = determine_target_dates(args, backup_cli)
    backup_cli.logger.info(f"Would process {len(dates)} dates: {[d.date() for d in dates]}")

    backup_cli.logger.info("=== DRY RUN COMPLETED SUCCESSFULLY ===")
    sys.exit(0)  # Exit successfully after dry run


def load_table_configuration(args: argparse.Namespace, backup_cli: TngdBackupCLI) -> List[str]:
    """
    Load table configuration from arguments or config file.

    Args:
        args: Parsed command line arguments
        backup_cli: Backup CLI instance

    Returns:
        List of table names to process
    """
    if args.tables:
        tables = args.tables
        backup_cli.logger.info(f"Using specified tables: {tables}")
    else:
        tables = backup_cli.load_table_list(args.config)

    return tables


def execute_backup_workflow(backup_cli: TngdBackupCLI, tables: List[str], dates: List[datetime]) -> Tuple[List[Dict[str, Any]], int, int, int]:
    """
    Execute the main backup workflow for all tables and dates.

    Args:
        backup_cli: Backup CLI instance
        tables: List of table names to backup
        dates: List of target dates

    Returns:
        Tuple of (backup_results, completed_operations, failed_operations, total_operations)
    """
    total_operations = len(dates) * len(tables)
    completed_operations = 0
    failed_operations = 0
    backup_results = []  # Collect detailed results for email reporting

    backup_cli.logger.info(f"Starting backup process: {total_operations} total operations")
    print(f"\n🚀 Starting backup process: {total_operations} total operations")
    print(f"📅 Processing {len(dates)} dates with {len(tables)} tables each")
    print("=" * 60)

    for date_idx, target_date in enumerate(dates, 1):
        date_completed, date_failed, date_results = process_date_backup(
            backup_cli, tables, target_date, date_idx, len(dates), total_operations
        )

        completed_operations += date_completed
        failed_operations += date_failed
        backup_results.extend(date_results)

    return backup_results, completed_operations, failed_operations, total_operations


def process_date_backup(backup_cli: TngdBackupCLI, tables: List[str], target_date: datetime,
                       date_idx: int, total_dates: int, total_operations: int) -> Tuple[int, int, List[Dict[str, Any]]]:
    """
    Process backup for all tables on a specific date.

    Args:
        backup_cli: Backup CLI instance
        tables: List of table names to backup
        target_date: Target date for backup
        date_idx: Current date index (1-based)
        total_dates: Total number of dates
        total_operations: Total number of operations

    Returns:
        Tuple of (completed_count, failed_count, results_list)
    """
    backup_cli.logger.info(f"Processing date: {target_date.date()}")
    print(f"\n📅 Processing date {date_idx}/{total_dates}: {target_date.date()}")

    date_completed = 0
    date_failed = 0
    date_results = []

    for table_idx, table_name in enumerate(tables, 1):
        # Progress indicator
        current_op = (date_idx - 1) * len(tables) + table_idx
        progress_percent = (current_op / total_operations) * 100

        print(f"  📊 [{current_op:3d}/{total_operations}] ({progress_percent:5.1f}%) Processing {table_name}...", end=" ")

        result = backup_cli.backup_table(table_name, target_date)

        # Process and log result
        completed, failed = process_backup_result(backup_cli, result, table_name, target_date)
        date_completed += completed
        date_failed += failed

        # Add detailed result for reporting
        detailed_result = create_detailed_result(result, table_name, target_date)
        date_results.append(detailed_result)

    # Date completion summary
    print(f"  📋 Date {target_date.date()} completed: {date_completed}/{len(tables)} successful")

    return date_completed, date_failed, date_results


def process_backup_result(backup_cli: TngdBackupCLI, result: Dict[str, Any],
                         table_name: str, target_date: datetime) -> Tuple[int, int]:
    """
    Process and log a single backup result with enhanced error handling.

    Args:
        backup_cli: Backup CLI instance
        result: Backup result dictionary
        table_name: Name of the table
        target_date: Target date

    Returns:
        Tuple of (completed_count, failed_count)
    """
    date_str = target_date.date().strftime('%Y-%m-%d')
    retry_config = backup_cli.config_manager.get('backup', 'retry_configuration', {})
    continue_on_failure = retry_config.get('continue_on_failure', True)

    if result['status'] == 'completed':
        file_size_mb = result.get('file_size_mb', 0)
        duration = result.get('duration_seconds', 0)
        print(f"[SUCCESS] ({file_size_mb:.1f}MB, {duration:.1f}s)")
        backup_cli.logger.info(f"[SUCCESS] {table_name}_{date_str}: Success ({file_size_mb:.1f}MB, {duration:.1f}s)")
        return 1, 0
    elif result['status'] == 'no_data':
        print(f"[NO_DATA] No data")
        backup_cli.logger.info(f"[NO_DATA] {table_name}_{date_str}: No data found")
        return 1, 0
    else:
        error_msg = result.get('error', 'Unknown error')

        if continue_on_failure:
            print(f"[FAILED] {error_msg} - CONTINUING")
            backup_cli.logger.warning(f"[FAILED] {table_name}_{date_str}: {error_msg} - Continuing with next table")
        else:
            print(f"[FAILED] {error_msg}")
            backup_cli.logger.error(f"[FAILED] {table_name}_{date_str}: {error_msg}")

        return 0, 1


def create_detailed_result(result: Dict[str, Any], table_name: str, target_date: datetime) -> Dict[str, Any]:
    """
    Create a detailed result dictionary for reporting.

    Args:
        result: Backup result dictionary
        table_name: Name of the table
        target_date: Target date

    Returns:
        Detailed result dictionary
    """
    return {
        'table_name': table_name,
        'target_date': target_date.date().strftime('%Y-%m-%d'),
        'status': result['status'],
        'file_size_mb': result.get('file_size_mb', 0),
        'duration_seconds': result.get('duration_seconds', 0),
        'upload_path': result.get('upload_path', ''),
        'start_time': result.get('start_time', ''),
        'end_time': result.get('end_time', ''),
        'error': result.get('error', '') if result['status'] not in ['completed', 'no_data'] else ''
    }


def generate_final_summary(backup_cli: TngdBackupCLI, backup_results: List[Dict[str, Any]],
                          completed_operations: int, failed_operations: int, total_operations: int,
                          dates: List[datetime], tables: List[str]) -> None:
    """
    Generate and display final backup summary with enhanced statistics and retry information.

    Args:
        backup_cli: Backup CLI instance
        backup_results: List of detailed backup results
        completed_operations: Number of completed operations
        failed_operations: Number of failed operations
        total_operations: Total number of operations
        dates: List of processed dates
        tables: List of processed tables
    """
    # Log final summary
    backup_cli.logger.info("=== BACKUP PROCESS COMPLETED ===")
    backup_cli.logger.info(f"Total operations: {total_operations}")
    backup_cli.logger.info(f"Completed successfully: {completed_operations}")
    backup_cli.logger.info(f"Failed: {failed_operations}")

    # Analyze failed operations
    failed_results = [r for r in backup_results if r['status'] not in ['completed', 'no_data']]
    failed_tables = set(r['table_name'] for r in failed_results)

    # Calculate and display statistics
    success_rate = (completed_operations / total_operations * 100) if total_operations > 0 else 0

    print(f"\n" + "=" * 60)
    print(f"🎯 BACKUP SUMMARY")
    print(f"=" * 60)
    print(f"📊 Total operations:      {total_operations}")
    print(f"✅ Completed successfully: {completed_operations}")
    print(f"❌ Failed:                {failed_operations}")
    print(f"📈 Success rate:          {success_rate:.1f}%")
    print(f"📅 Dates processed:       {len(dates)}")
    print(f"📋 Tables per date:       {len(tables)}")

    if failed_operations > 0:
        print(f"\n🔍 FAILED OPERATIONS ANALYSIS:")
        print(f"📋 Failed tables:         {len(failed_tables)}")
        print(f"📊 Failed table names:    {', '.join(sorted(failed_tables))}")

        # Generate retry information
        retry_config = backup_cli.config_manager.get('backup', 'retry_configuration', {})
        continue_on_failure = retry_config.get('continue_on_failure', True)

        if continue_on_failure:
            print(f"✅ Continue on failure:    ENABLED (backup continued despite failures)")
        else:
            print(f"❌ Continue on failure:    DISABLED (backup stopped on first failure)")

    # Get log file path
    log_file_path = os.path.join(backup_cli.config_manager.get('logging', 'log_dir', 'logs'), 'tngd_backup.log')

    # Generate retry script for failed tables if any
    if failed_operations > 0:
        _generate_retry_script(backup_cli, failed_results, dates)

    # Send email notification
    send_completion_notification(backup_cli, backup_results, dates)

    # Final status and exit
    retry_config = backup_cli.config_manager.get('backup', 'retry_configuration', {})
    continue_on_failure = retry_config.get('continue_on_failure', True)

    if failed_operations > 0:
        if continue_on_failure:
            print(f"\n⚠️  {failed_operations} operations failed, but backup completed for other tables.")
            print(f"📄 Log file: {log_file_path}")
            print(f"🔄 Check retry_failed_tables.txt for commands to retry failed tables")
            # Don't exit with error code if continue_on_failure is enabled
        else:
            print(f"\n⚠️  {failed_operations} operations failed. Check logs for details.")
            print(f"📄 Log file: {log_file_path}")
            sys.exit(1)
    else:
        print(f"\n🎉 All backup operations completed successfully!")
        print(f"📄 Log file: {log_file_path}")

def _generate_retry_script(backup_cli: TngdBackupCLI, failed_results: List[Dict[str, Any]],
                          dates: List[datetime]) -> None:
    """
    Generate a retry script for failed tables.

    Args:
        backup_cli: Backup CLI instance
        failed_results: List of failed backup results
        dates: List of processed dates
    """
    try:
        # Group failed results by date and table
        failed_by_date = {}
        for result in failed_results:
            date_str = result['target_date']
            if date_str not in failed_by_date:
                failed_by_date[date_str] = []
            failed_by_date[date_str].append(result['table_name'])

        # Generate retry script
        script_path = "retry_failed_tables.txt"
        with open(script_path, 'w') as f:
            f.write("# Retry script for failed backup tables\n")
            f.write("# Generated automatically by TNGD Backup System\n")
            f.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            for date_str, table_names in failed_by_date.items():
                f.write(f"# Retry failed tables for {date_str}\n")
                tables_str = " ".join([f'"{table}"' for table in table_names])
                f.write(f'python tngd_backup.py --start "{date_str}" --end "{date_str}" --tables {tables_str}\n\n')

        backup_cli.logger.info(f"Generated retry script: {script_path}")

    except Exception as e:
        backup_cli.logger.warning(f"Failed to generate retry script: {str(e)}")


def send_completion_notification(backup_cli: TngdBackupCLI, backup_results: List[Dict[str, Any]],
                               dates: List[datetime]) -> None:
    """
    Send email notification about backup completion.

    Args:
        backup_cli: Backup CLI instance
        backup_results: List of detailed backup results
        dates: List of processed dates
    """
    try:
        if backup_cli.notification_service and backup_cli.config_manager.get('notification', 'send_on_completion', True):
            backup_cli.logger.info("Sending backup completion email notification...")
            print("\n📧 Sending email notification...")

            # Determine backup type based on date range
            backup_type = "monthly" if len(dates) > 7 else "daily"

            email_sent = backup_cli.notification_service.send_backup_report(backup_results, backup_type)
            if email_sent:
                print("✅ Email notification sent successfully!")
                backup_cli.logger.info("Email notification sent successfully")
            else:
                print("⚠️  Failed to send email notification (check logs)")
                backup_cli.logger.warning("Failed to send email notification")
        else:
            backup_cli.logger.info("Email notifications disabled or not configured")
    except Exception as e:
        print(f"⚠️  Email notification error: {str(e)}")
        backup_cli.logger.error(f"Email notification error: {str(e)}")


def handle_error_notification(backup_cli: Optional[TngdBackupCLI], error: Exception) -> None:
    """
    Handle error notification sending.

    Args:
        backup_cli: Backup CLI instance (can be None)
        error: The exception that occurred
    """
    try:
        if backup_cli and backup_cli.notification_service and backup_cli.config_manager.get('notification', 'send_on_error', True):
            error_details = {
                'error_type': type(error).__name__,
                'error_message': str(error),
                'context': 'TNGD Backup Process'
            }
            backup_cli.notification_service.send_error_notification(error_details)
    except Exception as notification_error:
        if backup_cli and backup_cli.logger:
            backup_cli.logger.error(f"Failed to send error notification: {str(notification_error)}")


def main():
    """Main entry point for the TNGD Backup CLI."""
    backup_cli = None

    try:
        # Parse command line arguments
        args = parse_args()

        # Setup and validate environment
        backup_cli, tables, dates = setup_and_validate(args)

        # Execute backup workflow
        backup_results, completed_operations, failed_operations, total_operations = execute_backup_workflow(
            backup_cli, tables, dates
        )

        # Generate final summary and reports
        generate_final_summary(backup_cli, backup_results, completed_operations, failed_operations,
                             total_operations, dates, tables)

    except KeyboardInterrupt:
        print("\nBackup interrupted by user")
        if backup_cli:
            backup_cli.cleanup_all_temp_files()
        sys.exit(1)
    except Exception as e:
        print(f"Backup failed: {str(e)}")
        if backup_cli and backup_cli.logger:
            backup_cli.logger.error(f"Fatal error: {str(e)}")

        # Send error notification
        handle_error_notification(backup_cli, e)

        if backup_cli:
            backup_cli.cleanup_all_temp_files()
        sys.exit(1)
    finally:
        # Ensure cleanup
        if backup_cli:
            backup_cli.cleanup_all_temp_files()


def determine_target_dates(args: argparse.Namespace, backup_cli: TngdBackupCLI) -> List[datetime]:
    """
    Determine target dates based on command line arguments.

    Args:
        args: Parsed command line arguments
        backup_cli: Backup CLI instance

    Returns:
        List of target dates
    """
    if args.start and args.end:
        # Date range mode
        start_date = backup_cli.parse_date_string(args.start)
        end_date = backup_cli.parse_date_string(args.end)
        return backup_cli.generate_date_range(start_date, end_date)

    elif args.date_parts:
        # Single date mode
        date_str = ' '.join(args.date_parts)
        target_date = backup_cli.parse_date_string(date_str)
        return [target_date]

    else:
        # Default: today's data (actually yesterday for daily backups)
        yesterday = datetime.now() - timedelta(days=1)
        return [yesterday]


if __name__ == "__main__":
    main()
