#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced logging for chunked processing.
This will run a backup on a single large table to show the detailed progress logging.
"""

import sys
import subprocess
import time
from pathlib import Path

def run_test_with_large_table():
    """Run a test backup with a known large table to demonstrate chunked processing logging."""
    
    print("🧪 Testing Enhanced Logging for Chunked Processing")
    print("=" * 60)
    
    # Test with a large table that should trigger chunked processing
    test_command = [
        "python", "tngd_backup.py",
        "--start", "26 march 2025",
        "--end", "26 march 2025", 
        "--tables", "cloud.office365.management.exchange"
    ]
    
    print(f"🚀 Running command: {' '.join(test_command)}")
    print("📊 This should demonstrate:")
    print("   • Chunked processing detection")
    print("   • Detailed chunk progress logging")
    print("   • Real-time progress updates")
    print("   • Chunk combination process")
    print("   • Enhanced status messages")
    print("\n" + "=" * 60)
    
    try:
        # Run the command and capture output in real-time
        process = subprocess.Popen(
            test_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        
        print("\n" + "=" * 60)
        if return_code == 0:
            print("✅ Test completed successfully!")
            print("📊 Enhanced logging features demonstrated:")
            print("   • Chunked processing status updates")
            print("   • Progress bars and time estimates")
            print("   • Detailed chunk information")
            print("   • Real-time monitoring capabilities")
        else:
            print(f"⚠️  Test completed with return code: {return_code}")
            print("💡 Check the logs for detailed information")
        
        return return_code
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        if process:
            process.terminate()
        return 1
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return 1

def show_log_monitoring_tips():
    """Show tips for monitoring the backup process."""
    
    print("\n📋 MONITORING TIPS:")
    print("=" * 40)
    print("🔍 To monitor backup progress in real-time:")
    print("   • Watch the console output for progress updates")
    print("   • Check logs/tngd_backup.log for detailed logging")
    print("   • Look for these key indicators:")
    print("     🧩 CHUNKED PROCESSING STARTED")
    print("     🔄 CHUNK STARTED/COMPLETED messages")
    print("     📊 Progress bars and time estimates")
    print("     🔗 Chunk combination process")
    print("     ✅ CHUNKED BACKUP COMPLETED")
    
    print("\n📊 Enhanced Logging Features:")
    print("   • Real-time chunk progress with time estimates")
    print("   • Detailed processing rates (rows/second)")
    print("   • Progress bars for visual feedback")
    print("   • Step-by-step process breakdown")
    print("   • Clear status indicators with emojis")
    
    print("\n🚨 What to Look For:")
    print("   • If you see 'CHUNKED PROCESSING STARTED' - it's working!")
    print("   • Progress bars show chunk completion status")
    print("   • Time estimates help predict completion")
    print("   • 'No data' chunks are normal and expected")
    print("   • Deduplication process removes overlapping records")

def main():
    """Main test function."""
    
    print("🚀 Enhanced Logging Test for TNGD Backup System")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("tngd_backup.py").exists():
        print("❌ Error: tngd_backup.py not found in current directory")
        print("💡 Please run this script from the TNGD project root directory")
        return 1
    
    # Show monitoring tips first
    show_log_monitoring_tips()
    
    print("\n🎯 Starting Enhanced Logging Test...")
    print("⏰ This test will take several minutes to complete")
    
    # Ask user if they want to proceed
    try:
        response = input("\n❓ Do you want to proceed with the test? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("🚫 Test cancelled by user")
            return 0
    except KeyboardInterrupt:
        print("\n🚫 Test cancelled by user")
        return 0
    
    # Run the test
    return run_test_with_large_table()

if __name__ == "__main__":
    sys.exit(main())
