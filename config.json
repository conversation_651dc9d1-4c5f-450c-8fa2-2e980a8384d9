{"backup": {"default_days": 1, "default_chunk_size": 500000, "default_max_retries": 5, "default_retry_delay": 5, "default_timeout": 21600, "default_max_concurrent_tables": 1, "validate_backups": true, "table_timeout": 21600, "query_timeouts": {"count_query": 120, "small_table": 600, "medium_table": 1800, "large_table": 7200, "very_large_table": 21600, "ultra_large_table": 43200}, "table_size_thresholds": {"small_rows": 10000, "medium_rows": 100000, "large_rows": 1000000, "very_large_rows": 10000000, "ultra_large_rows": 50000000}, "retry_configuration": {"max_table_retries": 5, "retry_delay_base": 30, "retry_delay_max": 300, "exponential_backoff": true, "backoff_multiplier": 2, "connection_retry_attempts": 3, "skip_failed_tables": true, "continue_on_failure": true}, "chunking_configuration": {"enable_chunking": true, "chunk_by_hours": true, "hours_per_chunk": 6, "max_rows_per_chunk": 1000000, "chunk_overlap_minutes": 5, "auto_chunk_large_tables": true, "chunk_threshold_rows": 2000000}, "daily_backup": {"enabled": true, "schedule_time": "01:00", "max_duration_hours": 8, "retry_failed_tables": true, "cleanup_after_backup": true, "validate_after_backup": true}, "monthly_backup": {"enabled": true, "chunking_strategy": "day", "max_chunk_retries": 5, "retry_delay_minutes": 30, "parallel_chunks": false, "validate_chunks": true, "checkpoint_interval_hours": 2, "resource_management": {"dynamic_delays": true, "min_delay_seconds": 2, "max_delay_seconds": 60, "cpu_threshold_percent": 70, "memory_threshold_percent": 60, "disk_threshold_percent": 80, "adaptive_throttling": true}, "error_recovery": {"auto_retry_failed_days": true, "max_day_retries": 3, "exponential_backoff": true, "backoff_multiplier": 2, "max_backoff_seconds": 300, "failure_analysis": true, "detailed_error_logging": true}, "health_checks": {"enabled": true, "pre_backup_checks": true, "database_connectivity": true, "disk_space_check": true, "dependency_validation": true, "timeout_seconds": 30}, "configuration": {"table_config_paths": ["tables.json", "config/tabletest/tables.json", "backup/tables.json"], "fallback_table_list": ["my.app.tngd.waf", "my.app.tngd.actiontraillinux"], "validate_config_on_startup": true, "config_backup_enabled": true}}}, "storage": {"oss_path_template": "Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz", "summary_file_template": "backup_summary_{date_str}.json", "temp_dir": "temp", "compression_algorithm": "tar.gz", "compress_level": 6, "verify_compression": true, "save_checksums": true, "skip_oss_verification": true}, "logging": {"log_dir": "logs", "log_file": "backup.log", "max_files": 5, "max_size_mb": 10, "console_format": "minimal"}, "notification": {"send_on_completion": true, "send_on_error": true, "include_failed_tables": true, "daily_backup": {"enabled": true, "send_summary": true, "include_performance_metrics": true, "include_top_tables": 10}, "monthly_backup": {"enabled": true, "send_progress_updates": true, "progress_update_interval_hours": 6, "send_detailed_summary": true}}, "validation": {"checksum_algorithm": "md5", "validate_before_upload": true, "validate_after_upload": true, "retry_failed_validations": true, "max_validation_retries": 3, "validation_retry_delay": 5}, "performance_monitoring": {"enabled": true, "resource_monitoring": true, "save_performance_reports": true, "report_dir": "logs/performance", "alert_thresholds": {"cpu_percent": 80, "memory_percent": 90, "disk_percent": 85, "total_time_seconds": 3600}}}